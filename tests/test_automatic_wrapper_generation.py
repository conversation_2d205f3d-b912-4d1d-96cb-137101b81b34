"""Tests for automatic wrapper function generation."""

import pytest
import inspect
import async<PERSON>
from typing import Dict, Any, Union
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from albatross_kiotviet_mcp.tools.base.base_tool import BaseMCPTool
from albatross_kiotviet_mcp.tools.base.wrapper_generator import (
    create_wrapper_function_from_execute,
    validate_wrapper_function
)


class MockTool(BaseMCPTool):
    """Mock tool for testing wrapper generation."""
    
    def __init__(self):
        super().__init__(name="test_tool", description="Test tool for wrapper generation")
        self.api_client = Mock()
    
    async def execute(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> Dict[str, Any]:
        """Execute the test tool.
        
        Args:
            from_date: Start date (datetime object or ISO string)
            to_date: End date (datetime object or ISO string)
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            
        Returns:
            Dictionary containing test results
        """
        return {
            "from_date": str(from_date),
            "to_date": str(to_date),
            "page_size": page_size,
            "current_item": current_item,
            "order_direction": order_direction,
            "message": "Test execution successful"
        }
    
    def pre_execute(self, **kwargs) -> None:
        """Validate parameters."""
        pass


class TestWrapperGenerator:
    """Test cases for the wrapper generator utility."""
    
    def test_create_wrapper_function_from_execute(self):
        """Test creating wrapper function from execute method."""
        tool = MockTool()
        wrapper = create_wrapper_function_from_execute(tool)
        
        # Check wrapper properties
        assert callable(wrapper)
        assert inspect.iscoroutinefunction(wrapper)
        assert wrapper.__name__ == tool.name
        assert wrapper.__doc__ is not None
        
        # Check signature
        sig = inspect.signature(wrapper)
        execute_sig = inspect.signature(tool.execute)
        
        # Remove 'self' parameter from execute signature
        execute_params = [p for name, p in execute_sig.parameters.items() if name != 'self']
        wrapper_params = list(sig.parameters.values())
        
        assert len(execute_params) == len(wrapper_params)
        
        # Check parameter names and types match
        for exec_param, wrap_param in zip(execute_params, wrapper_params):
            assert exec_param.name == wrap_param.name
            assert exec_param.annotation == wrap_param.annotation
            assert exec_param.default == wrap_param.default
    
    @pytest.mark.asyncio
    async def test_wrapper_function_execution(self):
        """Test that the wrapper function executes correctly."""
        tool = MockTool()
        wrapper = create_wrapper_function_from_execute(tool)
        
        # Test wrapper execution
        result = await wrapper(
            from_date="2025-07-19",
            to_date="2025-07-20",
            page_size=25,
            current_item=10,
            order_direction="Desc"
        )
        
        expected = {
            "from_date": "2025-07-19",
            "to_date": "2025-07-20",
            "page_size": 25,
            "current_item": 10,
            "order_direction": "Desc",
            "message": "Test execution successful"
        }
        
        assert result == expected
    
    def test_validate_wrapper_function_valid(self):
        """Test validation of a valid wrapper function."""
        tool = MockTool()
        wrapper = create_wrapper_function_from_execute(tool)
        
        # Should not raise any exceptions
        assert validate_wrapper_function(wrapper, tool.name) is True
    
    def test_validate_wrapper_function_invalid_not_callable(self):
        """Test validation fails for non-callable."""
        with pytest.raises(ValueError, match="is not callable"):
            validate_wrapper_function("not_callable", "test_tool")
    
    def test_validate_wrapper_function_invalid_not_async(self):
        """Test validation fails for non-async function."""
        def sync_function():
            pass
        
        with pytest.raises(ValueError, match="must be async"):
            validate_wrapper_function(sync_function, "test_tool")
    
    def test_create_wrapper_function_missing_execute(self):
        """Test error handling when tool has no execute method."""
        tool = Mock()
        del tool.execute  # Remove execute method
        
        with pytest.raises(AttributeError, match="must have an execute method"):
            create_wrapper_function_from_execute(tool)


class TestBaseMCPToolIntegration:
    """Test cases for BaseMCPTool integration with automatic wrapper generation."""
    
    def test_get_wrapper_function_creates_wrapper(self):
        """Test that get_wrapper_function creates a wrapper automatically."""
        tool = MockTool()
        wrapper = tool.get_wrapper_function()
        
        assert callable(wrapper)
        assert inspect.iscoroutinefunction(wrapper)
        assert wrapper.__name__ == tool.name
    
    def test_get_wrapper_function_caches_wrapper(self):
        """Test that get_wrapper_function caches the wrapper."""
        tool = MockTool()
        wrapper1 = tool.get_wrapper_function()
        wrapper2 = tool.get_wrapper_function()
        
        # Should return the same instance
        assert wrapper1 is wrapper2
    
    @pytest.mark.asyncio
    async def test_wrapper_function_calls_execute(self):
        """Test that the wrapper function calls the execute method."""
        tool = MockTool()
        tool.execute = AsyncMock(return_value={"test": "result"})
        
        wrapper = tool.get_wrapper_function()
        result = await wrapper(
            from_date="2025-07-19",
            to_date="2025-07-20",
            page_size=25
        )
        
        # Verify execute was called with correct parameters
        tool.execute.assert_called_once_with(
            from_date="2025-07-19",
            to_date="2025-07-20",
            page_size=25
        )
        assert result == {"test": "result"}
    
    def test_fallback_wrapper_on_generation_failure(self):
        """Test that fallback wrapper is used when generation fails."""
        tool = MockTool()
        
        # Mock the wrapper generator to raise an exception
        original_create = tool._create_wrapper_function
        
        def mock_create():
            # Force an exception in the automatic generation
            raise Exception("Generation failed")
        
        tool._create_wrapper_function = mock_create
        
        # Should fall back to basic wrapper without raising
        wrapper = tool.get_wrapper_function()
        assert callable(wrapper)
        assert inspect.iscoroutinefunction(wrapper)

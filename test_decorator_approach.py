#!/usr/bin/env python3
"""Test the decorator-based approach."""

import sys
import asyncio
import inspect

# Add src to path
sys.path.insert(0, 'src')

from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
from albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool


async def test_decorator_approach():
    """Test the decorator-based approach."""
    print("🧪 Testing Decorator-Based Approach")
    print("=" * 50)
    
    try:
        # Create tool instance
        print("1. Creating tool instance...")
        config = get_config()
        client = KiotVietAPIClient(config)
        tool = CategoriesTool(client)
        print(f"✅ Tool created: {tool.name}")
        print(f"✅ Description: {tool.description}")
        
        # Get wrapper function
        print("\n2. Getting wrapper function...")
        wrapper = tool.get_wrapper_function()
        print("✅ Wrapper function obtained")
        
        # Check wrapper properties
        print(f"   Name: {wrapper.__name__}")
        print(f"   Is async: {inspect.iscoroutinefunction(wrapper)}")
        print(f"   Has docstring: {bool(wrapper.__doc__)}")
        
        # Check signature
        sig = inspect.signature(wrapper)
        print(f"   Signature: {sig}")
        
        print("\n🎉 Decorator approach working perfectly!")
        print("✨ No more manual _create_wrapper_function needed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_decorator_approach())
    sys.exit(0 if success else 1)

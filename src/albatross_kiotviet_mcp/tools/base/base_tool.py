"""Base MCP tool class with decorator-based wrapper generation."""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Callable

from .mcp_tool_decorator import get_mcp_wrapper, get_mcp_metadata

logger = logging.getLogger(__name__)


class BaseMCPTool(ABC):
    """Base class for all MCP tools.

    Tools should use the @mcp_tool decorator on their execute method
    to automatically generate wrapper functions for FastMCP registration.
    """

    def __init__(self, api_client):
        """Initialize the tool.

        Args:
            api_client: API client instance for making requests
        """
        self.api_client = api_client

        # Get metadata from the decorated execute method
        self.name, self.description = get_mcp_metadata(self.execute)
        self.logger = logging.getLogger(f"{__name__}.{self.name}")

    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters.

        This method should be decorated with @mcp_tool(name, description).

        Args:
            **kwargs: Tool-specific parameters

        Returns:
            Tool execution result
        """
        pass

    @abstractmethod
    def pre_execute(self, **kwargs) -> None:
        """Validate tool parameters before execution.

        Args:
            **kwargs: Parameters to validate

        Raises:
            ValueError: If parameters are invalid
        """
        pass

    def get_wrapper_function(self) -> Callable:
        """Get the wrapper function for FastMCP registration.

        Returns the wrapper function that was automatically generated
        by the @mcp_tool decorator.

        Returns:
            Callable function that wraps this tool's execute method
        """
        return get_mcp_wrapper(self)
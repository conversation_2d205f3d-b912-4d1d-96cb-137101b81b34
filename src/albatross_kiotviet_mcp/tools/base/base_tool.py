"""Base MCP tool class."""

import inspect
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable

from .wrapper_generator import create_wrapper_function_from_execute, validate_wrapper_function

logger = logging.getLogger(__name__)


class BaseMCPTool(ABC):
    """Base class for all MCP tools."""

    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self._wrapper_function: Optional[Callable] = None

    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters.

        Args:
            **kwargs: Tool-specific parameters

        Returns:
            Tool execution result
        """
        pass

    @abstractmethod
    def pre_execute(self, **kwargs) -> None:
        """Validate tool parameters before execution.

        Args:
            **kwargs: Parameters to validate

        Raises:
            ValueError: If parameters are invalid
        """
        pass

    def get_wrapper_function(self) -> Callable:
        """Get a wrapper function that can be registered with FastMCP.

        This creates a function with the proper signature that FastMCP can introspect
        and register as a tool. The wrapper function is automatically generated from
        the execute method signature, eliminating the need for manual implementation.

        Returns:
            Callable function that wraps this tool's execute method
        """
        if self._wrapper_function is None:
            self._wrapper_function = self._create_wrapper_function()
        return self._wrapper_function

    def _create_wrapper_function(self) -> Callable:
        """Create the wrapper function with proper signature.

        This method automatically generates a wrapper function from the execute method
        signature using Python's inspect module. Subclasses no longer need to override
        this method as the wrapper is generated automatically.

        Returns:
            Callable function with proper signature for FastMCP registration
        """
        try:
            wrapper = create_wrapper_function_from_execute(self)
            validate_wrapper_function(wrapper, self.name)
            logger.debug(f"Successfully created wrapper function for {self.name}")
            return wrapper
        except Exception as e:
            logger.error(f"Failed to create wrapper function for {self.name}: {e}")
            # Fallback to basic wrapper if automatic generation fails
            return self._create_fallback_wrapper()

    def _create_fallback_wrapper(self) -> Callable:
        """Create a fallback wrapper function if automatic generation fails.

        Returns:
            Basic wrapper function that accepts **kwargs
        """
        async def wrapper(**kwargs) -> Dict[str, Any]:
            """Fallback wrapper function."""
            return await self.execute(**kwargs)

        wrapper.__name__ = self.name
        wrapper.__doc__ = self.description
        logger.warning(f"Using fallback wrapper for {self.name}")
        return wrapper
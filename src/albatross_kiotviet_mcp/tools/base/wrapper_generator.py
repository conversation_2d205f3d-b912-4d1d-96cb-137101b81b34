"""Automatic wrapper function generation for MCP tools."""

import inspect
import logging
from typing import Dict, Any, Callable, get_type_hints

logger = logging.getLogger(__name__)


def create_wrapper_function_from_execute(tool_instance) -> Callable:
    """Create a wrapper function automatically from the tool's execute method.
    
    This function uses Python's inspect module to extract the signature of the
    execute method and create a wrapper function with the exact same signature,
    type hints, default values, and docstring. This eliminates the need for
    manual _create_wrapper_function implementations in tool subclasses.
    
    Args:
        tool_instance: The tool instance containing the execute method
        
    Returns:
        Callable function with proper signature for FastMCP registration
        
    Raises:
        ValueError: If the execute method signature cannot be extracted
        AttributeError: If the tool doesn't have an execute method
    """
    if not hasattr(tool_instance, 'execute'):
        raise AttributeError(f"Tool {tool_instance.__class__.__name__} must have an execute method")
    
    execute_method = tool_instance.execute
    
    # Get the signature of the execute method
    try:
        sig = inspect.signature(execute_method)
    except (ValueError, TypeError) as e:
        raise ValueError(f"Cannot extract signature from execute method: {e}")
    
    # Get type hints for the execute method
    try:
        type_hints = get_type_hints(execute_method)
    except (NameError, AttributeError) as e:
        logger.warning(f"Could not extract type hints from execute method: {e}")
        type_hints = {}
    
    # Extract parameters (excluding 'self' if present)
    params = []
    for param_name, param in sig.parameters.items():
        if param_name == 'self':
            continue
        params.append(param)
    
    # Create new signature for the wrapper function
    wrapper_sig = inspect.Signature(parameters=params, return_annotation=sig.return_annotation)
    
    # Get the docstring from the execute method
    execute_docstring = inspect.getdoc(execute_method) or f"Execute {tool_instance.name} tool."
    
    # Create the wrapper function dynamically
    def create_wrapper():
        async def wrapper(*args, **kwargs) -> Dict[str, Any]:
            """Dynamically generated wrapper function."""
            return await execute_method(*args, **kwargs)
        
        # Set the proper signature on the wrapper
        wrapper.__signature__ = wrapper_sig
        wrapper.__name__ = tool_instance.name
        wrapper.__doc__ = execute_docstring
        wrapper.__annotations__ = type_hints
        
        return wrapper
    
    return create_wrapper()


def validate_wrapper_function(wrapper_func: Callable, tool_name: str) -> bool:
    """Validate that a wrapper function has the required properties for FastMCP.
    
    Args:
        wrapper_func: The wrapper function to validate
        tool_name: Name of the tool for error messages
        
    Returns:
        True if the wrapper function is valid
        
    Raises:
        ValueError: If the wrapper function is invalid
    """
    # Check if it's a callable
    if not callable(wrapper_func):
        raise ValueError(f"Wrapper function for {tool_name} is not callable")
    
    # Check if it's async
    if not inspect.iscoroutinefunction(wrapper_func):
        raise ValueError(f"Wrapper function for {tool_name} must be async")
    
    # Check if it has a signature
    try:
        sig = inspect.signature(wrapper_func)
    except (ValueError, TypeError) as e:
        raise ValueError(f"Wrapper function for {tool_name} has invalid signature: {e}")
    
    # Check if it has a docstring
    if not wrapper_func.__doc__:
        logger.warning(f"Wrapper function for {tool_name} has no docstring")
    
    # Check if it has a name
    if not wrapper_func.__name__:
        raise ValueError(f"Wrapper function for {tool_name} has no name")
    
    logger.debug(f"Wrapper function for {tool_name} validated successfully")
    return True

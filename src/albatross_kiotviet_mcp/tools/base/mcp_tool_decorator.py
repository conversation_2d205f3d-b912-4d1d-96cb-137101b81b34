"""Decorator for automatic MCP tool wrapper generation."""

import functools
import inspect
from typing import Callable, Dict, Any


def mcp_tool(name: str, description: str):
    """Decorator that automatically creates MCP wrapper functions.
    
    This decorator eliminates the need for manual _create_wrapper_function
    implementations by automatically generating wrapper functions from the
    decorated execute method.
    
    Args:
        name: Tool name for FastMCP registration
        description: Tool description for FastMCP registration
        
    Usage:
        @mcp_tool("get_categories", "Get product categories from KiotViet API")
        async def execute(self, page_size: int = 50, current_item: int = 0) -> Dict[str, Any]:
            # Tool implementation
            pass
    """
    def decorator(execute_func: Callable) -> Callable:
        # Store metadata on the function
        execute_func._mcp_name = name
        execute_func._mcp_description = description

        # Get original signature and remove 'self' parameter
        original_sig = inspect.signature(execute_func)
        params = [p for name, p in original_sig.parameters.items() if name != 'self']
        new_sig = inspect.Signature(parameters=params, return_annotation=original_sig.return_annotation)

        # Create wrapper function with the same signature as execute (minus self)
        async def wrapper(*args, **kwargs) -> Dict[str, Any]:
            # We need to get the 'self' instance from somewhere
            # This will be handled when the wrapper is called from the tool instance
            return await execute_func(*args, **kwargs)

        # Set wrapper metadata for FastMCP
        wrapper.__name__ = name
        wrapper.__doc__ = description
        wrapper.__signature__ = new_sig

        # Store the wrapper on the execute function for later retrieval
        execute_func._mcp_wrapper = wrapper

        return execute_func
    
    return decorator


def get_mcp_wrapper(tool_instance) -> Callable:
    """Get the MCP wrapper function from a tool instance.

    Args:
        tool_instance: The tool instance with decorated execute method

    Returns:
        The wrapper function for FastMCP registration

    Raises:
        AttributeError: If the method is not decorated with @mcp_tool
    """
    execute_method = tool_instance.execute

    if not hasattr(execute_method, '_mcp_wrapper'):
        raise AttributeError(
            f"Method {execute_method.__name__} is not decorated with @mcp_tool. "
            "Use @mcp_tool(name, description) decorator on your execute method."
        )

    # Create a bound wrapper that calls the instance method
    original_wrapper = execute_method._mcp_wrapper

    async def bound_wrapper(*args, **kwargs) -> Dict[str, Any]:
        return await tool_instance.execute(*args, **kwargs)

    # Copy metadata from the original wrapper
    bound_wrapper.__name__ = original_wrapper.__name__
    bound_wrapper.__doc__ = original_wrapper.__doc__
    bound_wrapper.__signature__ = original_wrapper.__signature__

    return bound_wrapper


def get_mcp_metadata(execute_method: Callable) -> tuple[str, str]:
    """Get MCP metadata from a decorated execute method.
    
    Args:
        execute_method: The decorated execute method
        
    Returns:
        Tuple of (name, description)
        
    Raises:
        AttributeError: If the method is not decorated with @mcp_tool
    """
    if not hasattr(execute_method, '_mcp_name'):
        raise AttributeError(
            f"Method {execute_method.__name__} is not decorated with @mcp_tool"
        )
    
    return execute_method._mcp_name, execute_method._mcp_description

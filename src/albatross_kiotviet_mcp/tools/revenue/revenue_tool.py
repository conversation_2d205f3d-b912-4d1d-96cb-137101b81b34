"""Revenue calculation MCP tool implementation."""

from typing import Dict, Any, Union, List
from datetime import datetime
import logging
from ..base.base_tool import BaseMCPTool
from ...domain.interfaces.api_client import IKiotVietAPIClient
from ...domain.entities.invoice import Invoice

logger = logging.getLogger(__name__)


class RevenueToolError(Exception):
    """Custom exception for revenue tool errors."""
    pass


class RevenueTool(BaseMCPTool):
    """MCP tool for calculating daily revenue from KiotViet invoices."""

    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(
            name="calculate_daily_revenue",
            description="Calculate revenue metrics for a specific date range from KiotViet invoices"
        )
        self.api_client = api_client
    
    async def execute(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 100,
        include_details: bool = False
    ) -> Dict[str, Any]:
        """Execute the revenue calculation tool.

        Args:
            from_date: Start date (datetime object or ISO string)
            to_date: End date (datetime object or ISO string)
            page_size: Number of items per page for pagination (default: 100, max: 100)
            include_details: Whether to include detailed invoice breakdown (default: False)

        Returns:
            Dictionary containing revenue metrics and summary data
            
        Raises:
            RevenueToolError: If parameters are invalid or calculation fails
        """
        try:
            # Validate parameters
            self.pre_execute(
                from_date=from_date,
                to_date=to_date,
                page_size=page_size,
                include_details=include_details
            )
            
            # Format dates for display
            date_range = f"{from_date} to {to_date}"
            
            self.logger.info(
                f"Calculating revenue for date range: {date_range}, "
                f"page_size={page_size}, include_details={include_details}"
            )
            
            # Collect all invoices with pagination
            all_invoices = await self._collect_all_invoices(
                from_date=from_date,
                to_date=to_date,
                page_size=page_size
            )
            
            # Calculate revenue metrics
            revenue_metrics = self._calculate_revenue_metrics(all_invoices)
            
            # Build response
            result = {
                "date_range": date_range,
                "invoice_count": len(all_invoices),
                "gross_revenue": revenue_metrics["gross_revenue"],
                "net_revenue": revenue_metrics["net_revenue"],
                "payment_difference": revenue_metrics["payment_difference"],
                "average_invoice_value": revenue_metrics["average_invoice_value"],
                "total_discount": revenue_metrics["total_discount"]
            }
            
            # Add detailed breakdown if requested
            if include_details:
                result["invoice_details"] = self._build_invoice_details(all_invoices)
                result["daily_breakdown"] = self._build_daily_breakdown(all_invoices)
            
            self.logger.info(
                f"Revenue calculation completed: {result['invoice_count']} invoices, "
                f"gross: {result['gross_revenue']}, net: {result['net_revenue']}"
            )
            
            return result
            
        except ValueError as e:
            self.logger.error(f"Parameter validation failed: {e}")
            raise RevenueToolError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            self.logger.error(f"Failed to calculate revenue: {e}")
            raise RevenueToolError(f"Failed to calculate revenue: {str(e)}")

    async def _collect_all_invoices(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int
    ) -> List[Invoice]:
        """Collect all invoices for the date range using pagination.
        
        Args:
            from_date: Start date
            to_date: End date
            page_size: Items per page
            
        Returns:
            List of all invoices in the date range
        """
        all_invoices = []
        current_item = 0
        
        async with self.api_client:
            while True:
                # Get current page
                response = await self.api_client.get_invoices_by_day(
                    from_date=from_date,
                    to_date=to_date,
                    page_size=page_size,
                    current_item=current_item,
                    order_direction="Asc"
                )
                
                # Add invoices from current page
                all_invoices.extend(response.data)
                
                self.logger.debug(
                    f"Retrieved page: {len(response.data)} invoices, "
                    f"total so far: {len(all_invoices)}"
                )
                
                # Check if we've retrieved all invoices
                if len(response.data) < page_size or len(all_invoices) >= response.total:
                    break
                
                # Move to next page
                current_item += page_size
        
        self.logger.info(f"Collected {len(all_invoices)} total invoices")
        return all_invoices

    def _calculate_revenue_metrics(self, invoices: List[Invoice]) -> Dict[str, float]:
        """Calculate revenue metrics from invoice list.
        
        Args:
            invoices: List of invoice objects
            
        Returns:
            Dictionary with calculated metrics
        """
        if not invoices:
            return {
                "gross_revenue": 0.0,
                "net_revenue": 0.0,
                "payment_difference": 0.0,
                "average_invoice_value": 0.0,
                "total_discount": 0.0
            }
        
        gross_revenue = sum(invoice.total or 0.0 for invoice in invoices)
        net_revenue = sum(invoice.totalPayment or 0.0 for invoice in invoices)
        total_discount = sum(invoice.discount or 0.0 for invoice in invoices)
        
        return {
            "gross_revenue": gross_revenue,
            "net_revenue": net_revenue,
            "payment_difference": gross_revenue - net_revenue,
            "average_invoice_value": gross_revenue / len(invoices) if invoices else 0.0,
            "total_discount": total_discount
        }

    def _build_invoice_details(self, invoices: List[Invoice]) -> List[Dict[str, Any]]:
        """Build detailed invoice breakdown.
        
        Args:
            invoices: List of invoice objects
            
        Returns:
            List of invoice detail dictionaries
        """
        return [
            {
                "id": invoice.id,
                "code": invoice.code,
                "purchase_date": invoice.purchaseDate,
                "total": invoice.total or 0.0,
                "total_payment": invoice.totalPayment or 0.0,
                "discount": invoice.discount or 0.0,
                "branch_name": invoice.branchName,
                "customer_name": invoice.customerName,
                "status": invoice.status
            }
            for invoice in invoices
        ]

    def _build_daily_breakdown(self, invoices: List[Invoice]) -> Dict[str, Dict[str, Any]]:
        """Build daily revenue breakdown.
        
        Args:
            invoices: List of invoice objects
            
        Returns:
            Dictionary with daily breakdown
        """
        daily_data = {}
        
        for invoice in invoices:
            if not invoice.purchaseDate:
                continue
                
            # Extract date part
            date_key = invoice.purchaseDate.split('T')[0]
            
            if date_key not in daily_data:
                daily_data[date_key] = {
                    "invoice_count": 0,
                    "gross_revenue": 0.0,
                    "net_revenue": 0.0,
                    "total_discount": 0.0
                }
            
            daily_data[date_key]["invoice_count"] += 1
            daily_data[date_key]["gross_revenue"] += invoice.total or 0.0
            daily_data[date_key]["net_revenue"] += invoice.totalPayment or 0.0
            daily_data[date_key]["total_discount"] += invoice.discount or 0.0
        
        return daily_data

    def pre_execute(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 100,
        include_details: bool = False
    ) -> None:
        """Validate tool parameters before execution.

        Args:
            from_date: Start date to validate
            to_date: End date to validate
            page_size: Page size to validate
            include_details: Include details flag to validate

        Raises:
            ValueError: If parameters are invalid
        """
        # Validate dates
        if not from_date or not to_date:
            raise ValueError("from_date and to_date are required")
        
        # Validate page_size
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        # Validate include_details
        if not isinstance(include_details, bool):
            raise ValueError("include_details must be a boolean")
        
        # Validate date range using utility function
        try:
            from ...utils.date_utils import validate_date_range
            validate_date_range(from_date, to_date)
        except Exception as e:
            raise ValueError(f"Invalid date range: {str(e)}")

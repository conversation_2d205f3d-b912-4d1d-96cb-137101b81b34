"""Categories MCP tool implementation."""

from typing import Dict, Any, Callable
import logging
from ..base.base_tool import <PERSON><PERSON>PTool
from ..base.mcp_tool_decorator import mcp_tool
from ...domain.interfaces.api_client import IKiotVietAPIClient

logger = logging.getLogger(__name__)


class CategoriesToolError(Exception):
    """Custom exception for categories tool errors."""
    pass


class CategoriesTool(BaseMCPTool):
    """MCP tool for retrieving product categories from KiotViet."""

    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(api_client)

    @mcp_tool("get_categories", "Get product categories from KiotViet API with pagination support")
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Execute the categories tool.

        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            hierarchical_data: Whether to return hierarchical structure (default: False)

        Returns:
            Dictionary containing categories data and pagination info
        """
        try:
            
            # Validate parameters
            self.pre_execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )
            
            self.logger.info(
                f"Fetching categories: page_size={page_size}, "
                f"current_item={current_item}, order_direction={order_direction}, "
                f"hierarchical_data={hierarchical_data}"
            )
            
            # Make API call
            async with self.api_client:
                typed_result = await self.api_client.get_categories(
                    page_size=page_size,
                    current_item=current_item,
                    order_direction=order_direction,
                    hierarchical_data=hierarchical_data
                )

            self.logger.info(f"Successfully retrieved categories: {len(typed_result.data)} items")

            return typed_result.model_dump()
            
        except ValueError as e:
            self.logger.error(f"Invalid parameters: {e}")
            raise CategoriesToolError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            self.logger.error(f"Failed to retrieve categories: {e}")
            raise CategoriesToolError(f"Failed to retrieve categories: {str(e)}")

    def pre_execute(self, **kwargs) -> None:
        """Validate categories tool parameters.
        
        Args:
            **kwargs: Parameters to validate
            
        Raises:
            ValueError: If parameters are invalid
        """
        page_size = kwargs.get('page_size', 50)
        current_item = kwargs.get('current_item', 0)
        order_direction = kwargs.get('order_direction', 'Asc')
        
        # Validate page_size
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        # Validate current_item
        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")
        
        # Validate order_direction
        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")


    def _create_wrapper_function(self) -> Callable:
        """Create wrapper function with proper signature for FastMCP registration."""
        async def get_categories(
            page_size: int = 50,
            current_item: int = 0,
            order_direction: str = "Asc",
            hierarchical_data: bool = False
        ) -> Dict[str, Any]:
            """Get product categories from KiotViet API.

            Args:
                page_size: Number of items per page (default: 50, max: 100)
                current_item: Starting item index for pagination (default: 0)
                order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
                hierarchical_data: Whether to return hierarchical structure (default: False)

            Returns:
                Dictionary containing categories data and pagination info
            """
            return await self.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )

        return get_categories


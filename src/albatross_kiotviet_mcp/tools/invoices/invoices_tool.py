"""Invoices MCP tool implementation."""

from typing import Dict, Any, Union
from datetime import datetime
import logging
from ..base.base_tool import BaseMCPTool
from ...domain.interfaces.api_client import IKiotVietAPIClient

logger = logging.getLogger(__name__)


class InvoicesToolError(Exception):
    """Custom exception for invoices tool errors."""
    pass


class InvoicesTool(BaseMCPTool):
    """MCP tool for retrieving invoices from KiotViet."""

    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(
            name="get_invoices_by_day",
            description="Get invoices for a specific date range from KiotViet API with pagination support"
        )
        self.api_client = api_client
    
    async def execute(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> Dict[str, Any]:
        """Execute the invoices tool.

        Args:
            from_date: Start date (datetime object or ISO string)
            to_date: End date (datetime object or ISO string)
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")

        Returns:
            Dictionary containing invoices data and pagination info
            
        Raises:
            InvoicesToolError: If parameters are invalid or API call fails
        """
        try:
            # Validate parameters
            self.pre_execute(
                from_date=from_date,
                to_date=to_date,
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction
            )
            
            self.logger.info(
                f"Fetching invoices: from_date={from_date}, to_date={to_date}, "
                f"page_size={page_size}, current_item={current_item}, "
                f"order_direction={order_direction}"
            )
            
            # Make API call
            async with self.api_client:
                typed_result = await self.api_client.get_invoices_by_day(
                    from_date=from_date,
                    to_date=to_date,
                    page_size=page_size,
                    current_item=current_item,
                    order_direction=order_direction
                )

            self.logger.info(f"Successfully retrieved invoices: {len(typed_result.data)} items")

            return typed_result.model_dump()
            
        except ValueError as e:
            self.logger.error(f"Parameter validation failed: {e}")
            raise InvoicesToolError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            self.logger.error(f"Failed to retrieve invoices: {e}")
            raise InvoicesToolError(f"Failed to retrieve invoices: {str(e)}")

    def pre_execute(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> None:
        """Validate tool parameters before execution.

        Args:
            from_date: Start date to validate
            to_date: End date to validate
            page_size: Page size to validate
            current_item: Current item index to validate
            order_direction: Order direction to validate

        Raises:
            ValueError: If parameters are invalid
        """
        # Validate page_size
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        # Validate current_item
        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")
        
        # Validate order_direction
        if order_direction not in ["Asc", "Desc"]:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
        
        # Validate dates (basic validation - detailed validation happens in API client)
        if not from_date or not to_date:
            raise ValueError("from_date and to_date are required")
